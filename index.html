<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MeetAugust Calorie Calculator</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        color: #374151;
        line-height: 1.6;
      }
      #container {
        max-width: 1200px;
        margin: 0 auto;
      }
      header {
        background-color: #ffffff;
        padding: 4px 0px;
        border-bottom: 1px solid #e5e7eb;
        position: sticky;
        top: 0;
        z-index: 1000;
      }
      .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1230px;
        margin: 0 auto;
      }
      .logo {
        display: flex;
        align-items: center;
      }
      .logo img {
        height: 60px;
        width: auto;
      }
      .nav {
        display: flex;
        align-items: center;
        gap: 32px;
      }
      .nav-links {
        display: flex;
        align-items: center;
        gap: 24px;
        list-style: none;
        margin: 0;
        padding: 0;
      }
      .nav-links a {
        color: #374151;
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
        transition: color 0.2s ease;
      }
      .nav-links a:hover {
        color: #416955;
      }
      .nav-links a.active {
        color: #416955;
      }
      .talk-to-august {
        background-color: #416955;
        color: white !important;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
        transition: background-color 0.2s ease;
      }
      .talk-to-august:hover {
        background-color: #2d4a3a;
        color: white !important;
      }
      main {
        /* padding: 40px 20px; */
        /* max-width: 800px; */
        /* margin: 0 auto; */
        /* background-color: #ffffff; */
        /* border-radius: 8px; */
        /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
        margin-top: 20px;
        margin-bottom: 20px;
      }
      h1 {
        color: #416955;
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      p {
        color: #6b7280;
        font-size: 16px;
        margin-bottom: 20px;
      }
      .print-link {
        text-align: right;
        margin-bottom: 10px;
      }
      .print-link a {
        color: #416955;
        text-decoration: none;
        font-weight: 500;
      }
      .print-link a:hover {
        color: #2d4a3a;
      }
      .form-container {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 30px;
        margin-top: 20px;
      }
      .tabs {
        margin-bottom: 10px;
      }
      .tab-button {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        padding: 8px 16px;
        cursor: pointer;
        border-radius: 6px 6px 0 0;
        font-weight: 500;
        color: #6b7280;
        transition: all 0.2s ease;
      }
      .tab-button:hover {
        background-color: #e5e7eb;
        color: #374151;
      }
      .tab-button.active {
        background-color: white;
        border-bottom: 2px solid #416955;
        color: #416955;
      }
      .form-instruction {
        color: #6b7280;
        font-size: 16px;
        margin-bottom: 16px;
      }
      .form-field {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 12px;
      }
      .form-field label {
        display: inline-block;
        width: 100px;
        color: #374151;
        font-weight: 500;
      }
      .form-field input[type="text"] {
        width: 100px;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 14px;
        transition: border-color 0.2s ease;
      }
      .form-field input[type="text"]:focus {
        outline: none;
        border-color: #416955;
        box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
      }
      .form-field select {
        width: 400px;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 14px;
        transition: border-color 0.2s ease;
      }
      .form-field select:focus {
        outline: none;
        border-color: #416955;
        box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
      }
      .form-field input[type="radio"] {
        margin-right: 6px;
        accent-color: #416955;
      }
      .form-field span {
        color: #6b7280;
        font-size: 14px;
      }
      .settings-link {
        margin-bottom: 20px;
      }
      .settings-link a {
        color: #416955;
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
      }
      .settings-link a:hover {
        color: #2d4a3a;
      }
      .form-buttons {
        text-align: left;
        margin-top: 30px;
      }
      .calculate-button {
        background-color: #416955;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        margin-right: 12px;
        cursor: pointer;
        font-weight: 500;
        font-size: 16px;
        transition: background-color 0.2s ease;
      }
      .calculate-button:hover {
        background-color: #2d4a3a;
      }
      .clear-button {
        background-color: #f3f4f6;
        color: #374151;
        padding: 12px 24px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        font-size: 16px;
        transition: all 0.2s ease;
      }
      .clear-button:hover {
        background-color: #e5e7eb;
        border-color: #9ca3af;
      }
      .result {
        margin-top: 30px;
        padding: 20px;
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        color: #416955;
        font-size: 18px;
        font-weight: 600;
      }
      .activity-definitions {
        margin-top: 30px;
        padding: 20px;
        background-color: #f9fafb;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .activity-definitions h3 {
        color: #374151;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .activity-definitions ul {
        list-style-type: disc;
        padding-left: 20px;
        color: #6b7280;
        font-size: 14px;
        line-height: 1.6;
      }
      .activity-definitions li {
        margin-bottom: 8px;
      }

      /* Converter Section Styles */
      .converter-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #f9fafb;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .converter-section h2 {
        color: #416955;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .converter-container {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 20px;
        flex-wrap: wrap;
      }
      .converter-input,
      .converter-output {
        display: flex;
        align-items: center;
        gap: 12px;
      }
      .converter-input input {
        width: 100px;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 14px;
      }
      .converter-input select,
      .converter-output select {
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 14px;
        min-width: 200px;
      }
      .converter-equals {
        font-size: 18px;
        font-weight: 600;
        color: #416955;
      }
      #converted-value {
        font-size: 18px;
        font-weight: 600;
        color: #416955;
        min-width: 80px;
      }

      /* Related Section Styles */
      .related-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #f9fafb;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .related-section h2 {
        color: #416955;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 20px;
      }
      .related-links {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
      }
      .related-link {
        background-color: #416955;
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        transition: background-color 0.2s ease;
      }
      .related-link:hover {
        background-color: #2d4a3a;
      }

      /* Information Section Styles */
      .info-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .info-section h2 {
        color: #416955;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .info-section h3 {
        color: #374151;
        font-size: 20px;
        font-weight: 600;
        margin: 24px 0 16px 0;
      }
      .equations-container {
        margin-top: 24px;
      }
      .equation-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
      .equation-card h4 {
        color: #416955;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .equation-card p {
        margin-bottom: 8px;
        font-family: "Courier New", monospace;
        background-color: #f3f4f6;
        padding: 8px;
        border-radius: 4px;
        font-size: 14px;
      }
      .equation-note {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
        background-color: transparent !important;
        padding: 0 !important;
        font-style: italic;
        color: #6b7280;
        font-size: 14px;
      }
      .weight-guidance {
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
      }
      .weight-guidance h4 {
        color: #416955;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .weight-guidance ul {
        list-style-type: disc;
        padding-left: 20px;
        margin-bottom: 16px;
      }
      .weight-guidance li {
        margin-bottom: 8px;
        color: #374151;
      }
      .warning-note {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 12px;
        color: #92400e;
        font-weight: 500;
        margin-top: 16px;
      }

      /* Additional Info Sections */
      .info-text {
        margin-top: 20px;
      }
      .info-text p {
        background-color: transparent !important;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
        padding: 0 !important;
        margin-bottom: 16px;
        line-height: 1.6;
      }

      /* Counting Section Styles */
      .counting-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .counting-section h2 {
        color: #416955;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .steps-container {
        display: grid;
        gap: 20px;
        margin-top: 24px;
      }
      .step-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        border-left: 4px solid #416955;
      }
      .step-card h4 {
        color: #416955;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      /* Zigzag Section Styles */
      .zigzag-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .zigzag-section h2 {
        color: #416955;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .zigzag-explanation {
        margin-top: 24px;
      }
      .zigzag-explanation h3 {
        color: #374151;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .example-card,
      .benefits-card {
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }
      .example-card h4,
      .benefits-card h4 {
        color: #416955;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      /* Requirements Section Styles */
      .requirements-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .requirements-section h2 {
        color: #416955;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .factors-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 24px;
      }
      .factor-card,
      .guidelines-card,
      .minimum-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
      }
      .factor-card h4,
      .guidelines-card h4,
      .minimum-card h4 {
        color: #416955;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .note {
        font-style: italic;
        color: #6b7280;
        font-size: 14px;
      }
      .warning {
        color: #dc2626;
        font-weight: 500;
      }

      /* Types Section Styles */
      .types-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .types-section h2 {
        color: #416955;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .calorie-types {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 24px;
      }
      .type-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
      }
      .type-card h4 {
        color: #416955;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .thermic-effect {
        margin-top: 30px;
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 20px;
      }
      .thermic-effect h3 {
        color: #416955;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img
            width="200"
            src="./assets/august_logo_green_nd4fn9.svg"
            alt="Calculator Logo"
          />
        </div>
        <div class="nav">
          <a href="#" class="talk-to-august">Talk to August</a>
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>MeetAugust Calorie Calculator</h1>
        <p>
          The Calorie Calculator can be used to estimate the number of calories
          a person needs to consume each day. This calculator can also provide
          some simple guidelines for gaining or losing weight.
        </p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button">US Units</button>
            <button class="tab-button active">Metric Units</button>
          </div>
          <p class="form-instruction">
            Modify the values and click the Calculate button to use
          </p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">Age</label>
              <input type="text" id="age" value="25" />
              <span>ages 15 - 80</span>
            </div>
            <div class="form-field">
              <label>Gender</label>
              <input type="radio" name="gender" value="male" checked /> male
              <input type="radio" name="gender" value="female" /> female
            </div>
            <div class="form-field">
              <label for="height">Height</label>
              <input type="text" id="height" value="180" />
              <span>cm</span>
            </div>
            <div class="form-field">
              <label for="weight">Weight</label>
              <input type="text" id="weight" value="65" />
              <span>kg</span>
            </div>
            <div class="form-field">
              <label for="activity">Activity</label>
              <select id="activity">
                <option value="sedentary">
                  Sedentary: little or no exercise
                </option>
                <option value="light">
                  Lightly active: light exercise 1-3 days/week
                </option>
                <option value="moderate" selected>
                  Moderately active: moderate exercise 3-5 days/week
                </option>
                <option value="very">
                  Very active: hard exercise 6-7 days/week
                </option>
                <option value="super">
                  Super active: very hard exercise, physical job
                </option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#">+ Settings</a>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                Calculate ▶
              </button>
              <button type="button" class="clear-button">Clear</button>
            </div>
          </form>
        </section>
        <div id="result" class="result"></div>
        <div class="activity-definitions">
          <h3>Exercise Definitions</h3>
          <ul>
            <li>Exercise: 15-30 minutes of elevated heart rate activity.</li>
            <li>
              Intense exercise: 45-120 minutes of elevated heart rate activity.
            </li>
            <li>
              Very intense exercise: 2+ hours of elevated heart rate activity.
            </li>
          </ul>
        </div>

        <!-- Food Energy Converter Section -->
        <!-- <section class="converter-section">
          <h2>Food Energy Converter</h2>
          <p>
            The following converter can be used to convert between Calories and
            other common food energy units.
          </p>

          <div class="converter-container">
            <div class="converter-input">
              <input type="number" id="converter-value" value="1" />
              <select id="from-unit">
                <option value="kcal">Calorie [Nutritional, kcal]</option>
                <option value="cal">calorie [cal]</option>
                <option value="kj">Kilojoules [kJ]</option>
                <option value="j">joules [J]</option>
              </select>
            </div>
            <div class="converter-equals">=</div>
            <div class="converter-output">
              <span id="converted-value">4.1868</span>
              <select id="to-unit">
                <option value="kcal">Calorie [Nutritional, kcal]</option>
                <option value="cal">calorie [cal]</option>
                <option value="kj" selected>Kilojoules [kJ]</option>
                <option value="j">joules [J]</option>
              </select>
            </div>
          </div>
        </section> -->

        <!-- Related Calculators Section -->
        <!-- <section class="related-section">
          <h2>Related Calculators</h2>
          <div class="related-links">
            <a href="#" class="related-link">BMI Calculator</a>
            <a href="#" class="related-link">Body Fat Calculator</a>
            <a href="#" class="related-link">Ideal Weight Calculator</a>
          </div>
        </section> -->

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>How This Calorie Calculator Supports Your Health Goals</h2>
          <p>
            Maintaining good health begins with understanding how many calories
            your body needs daily. Our Calorie Calculator helps you estimate
            your Basal Metabolic Rate (BMR)—the number of calories your body
            burns at rest—using proven scientific formulas. This tool is ideal
            for health-conscious individuals, fitness beginners, weight loss
            seekers, and anyone focused on nutrition.
          </p>

          <div class="equations-container">
            <h3>The Science Behind the Calculator</h3>
            <p>
              This calculator uses three widely accepted equations to estimate
              your daily energy expenditure:
            </p>

            <div class="equation-card">
              <h4>
                🧠 1. Mifflin-St Jeor Equation (Most Accurate for General Use)
              </h4>
              <p>
                <strong>For men:</strong> BMR = 10 × weight (kg) + 6.25 × height
                (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>For women:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                The Mifflin-St Jeor Equation is currently considered the most
                accurate method for estimating BMR in the general population.
              </p>
            </div>

            <div class="equation-card">
              <h4>
                🧮 2. Revised Harris-Benedict Equation (Improved Legacy Formula)
              </h4>
              <p>
                <strong>For men:</strong> BMR = 13.397 × weight + 4.799 × height
                - 5.677 × age + 88.362
              </p>
              <p>
                <strong>For women:</strong> BMR = 9.247 × weight + 3.098 ×
                height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                This equation updates the original Harris-Benedict formula from
                1919, offering better accuracy for modern users.
              </p>
            </div>

            <div class="equation-card">
              <h4>💪 3. Katch-McArdle Formula (Best for Lean Individuals)</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat %) × weight (kg)</p>
              <p class="equation-note">
                Unlike the other equations, the Katch-McArdle Formula considers
                lean body mass, making it ideal for athletes and those who know
                their body fat percentage.
              </p>
            </div>

            <div class="info-text">
              <h3>From BMR to Calorie Needs</h3>
              <p>
                Once your BMR is calculated, it's adjusted based on your
                activity level:
              </p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Activity Level
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Description
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Multiplier
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sedentary
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Little to no exercise
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Lightly active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Light exercise 1–3 days/week
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Moderately active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Moderate exercise 3–5 days/week
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Very active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Intense exercise 6–7 days/week
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Super active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Very intense daily exercise or job
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>
                This gives your Total Daily Energy Expenditure (TDEE)—the number
                of calories you need to maintain your current weight.
              </p>

              <h3>Healthy Weight Loss: What You Should Know</h3>
              <p>
                To lose weight safely, reduce your daily calorie intake by
                around 500 calories, which can lead to a 1-pound (0.45 kg) loss
                per week. This aligns with health guidelines recommending slow
                and steady progress for sustainable results.
              </p>

              <div class="warning-note">
                <strong
                  >⚠️ Avoid cutting more than 1,000 calories per day or losing
                  over 2 pounds per week, as this can:</strong
                >
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>Lower your metabolism</li>
                  <li>Result in muscle loss</li>
                  <li>Cause nutrient deficiencies</li>
                  <li>Lead to weight regain due to unsustainable habits</li>
                </ul>
                <p style="margin-top: 12px">
                  Instead, combine balanced nutrition, regular exercise, and
                  adequate hydration to support long-term health.
                </p>
              </div>

              <h3>Final Health Tips</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>Don't just track calories—track nutrient quality too.</li>
                <li>Include enough fiber, protein, and essential vitamins.</li>
                <li>Avoid extreme or crash diets.</li>
                <li>Focus on building habits, not just short-term goals.</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                This calculator is a helpful tool to start your health journey,
                whether your goal is weight loss, muscle gain, or simply
                maintaining a healthy lifestyle.
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>Calorie Counting as a Means for Weight Loss</h2>
          <p>
            Calorie counting with the intent of losing weight, on its simplest
            levels, can be broken down into a few general steps:
          </p>

          <div class="steps-container">
            <div class="step-card">
              <h4>1. Determine your BMR</h4>
              <p>
                Use one of the provided equations. If you know your body fat
                percentage, the Katch-McArdle Formula might be a more accurate
                representation of your BMR. Remember that the values attained
                from these equations are approximations.
              </p>
            </div>

            <div class="step-card">
              <h4>2. Set weight loss goals</h4>
              <p>
                Recall that 1 pound (~0.45 kg) equates to approximately 3500
                calories, and reducing daily caloric intake by 500 calories per
                day will theoretically result in a loss of 1 pound a week. It is
                generally not advisable to lose more than 2 pounds per week.
              </p>
            </div>

            <div class="step-card">
              <h4>3. Choose a tracking method</h4>
              <p>
                If you have a smartphone, there are many easy-to-use
                applications that facilitate tracking calories, exercise, and
                progress. Many have estimates for calories in brand-name foods
                or restaurant dishes.
              </p>
            </div>

            <div class="step-card">
              <h4>4. Track your progress</h4>
              <p>
                Remember that weight loss alone is not the sole determinant of
                health and fitness. Take measurements over longer periods
                (weekly rather than daily) as significant variations can occur
                based on water intake or time of day.
              </p>
            </div>

            <div class="step-card">
              <h4>5. Keep at it!</h4>
              <p>
                Calorie counting is not an exact science, and can be as complex
                as you want to make it. Finding an approach that fits within
                your lifestyle is likely to provide the most sustainable result.
              </p>
            </div>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>Zigzag Calorie Cycling</h2>
          <p>
            Zigzag calorie cycling is a weight loss approach that aims to
            counteract the human body's natural adaptive tendencies. Counting
            and restricting calories is a viable method to lose weight, but over
            time, it is possible for the body to adapt to the lower number of
            calories consumed.
          </p>

          <div class="zigzag-explanation">
            <h3>How It Works</h3>
            <p>
              Zigzag calorie cycling involves alternating the number of calories
              consumed on a given day. A person on a zigzag diet should have a
              combination of high-calorie and low-calorie days to meet the same
              overall weekly calorie target.
            </p>

            <div class="example-card">
              <h4>Example</h4>
              <p>
                If your target calorie intake is 14,000 calories per week, you
                could:
              </p>
              <ul>
                <li>
                  Consume 2,300 calories three days a week, and 1,775 the other
                  four days
                </li>
                <li>Or consume 2,000 calories each day</li>
              </ul>
              <p>
                In both cases, 14,000 calories would be consumed over the week,
                but the body wouldn't adapt and compensate for a 2,000-calorie
                diet.
              </p>
            </div>

            <div class="benefits-card">
              <h4>Benefits</h4>
              <ul>
                <li>Prevents metabolic adaptation</li>
                <li>Allows flexibility for social occasions</li>
                <li>Can include "cheat days" without guilt</li>
                <li>May help break weight loss plateaus</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>How Many Calories Do You Need?</h2>
          <p>
            Many people seek to lose weight, and often the easiest way to do
            this is to consume fewer calories each day. But how many calories
            does the body actually need in order to be healthy?
          </p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>Factors That Influence Calorie Needs</h4>
              <ul>
                <li>Age</li>
                <li>Weight and height</li>
                <li>Sex</li>
                <li>Physical activity levels</li>
                <li>Overall general health</li>
                <li>Muscle mass</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>General Guidelines (U.S. Department of Health)</h4>
              <p><strong>Adult Males:</strong> 2,000-3,000 calories per day</p>
              <p>
                <strong>Adult Females:</strong> 1,600-2,400 calories per day
              </p>
              <p class="note">
                These ranges vary significantly based on age and activity level.
              </p>
            </div>

            <div class="minimum-card">
              <h4>Minimum Safe Intake (Harvard Health)</h4>
              <p><strong>Women:</strong> At least 1,200 calories per day</p>
              <p><strong>Men:</strong> At least 1,500 calories per day</p>
              <p class="warning">Unless supervised by doctors</p>
            </div>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>Calories: Different Kinds and Their Effects</h2>
          <p>
            The main sources of calories in a typical person's diet are
            carbohydrates, proteins, and fat, with alcohol also being a
            significant portion of calorie intake for many people.
          </p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>High-Calorie Foods</h4>
              <p>
                Foods that are calorically dense, meaning high calories relative
                to serving size.
              </p>
              <ul>
                <li>Fats and oils</li>
                <li>Fried foods</li>
                <li>Sugary foods</li>
                <li>Nuts and seeds (healthy option)</li>
                <li>Avocados (healthy option)</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Low-Calorie Foods</h4>
              <p>Foods with fewer calories relative to serving size.</p>
              <ul>
                <li>Most vegetables</li>
                <li>Certain fruits</li>
                <li>Lean proteins</li>
                <li>Whole grains</li>
                <li>Leafy greens</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Empty Calories</h4>
              <p>Calories that contain few to no nutrients.</p>
              <ul>
                <li>Added sugars</li>
                <li>Solid fats</li>
                <li>Alcohol</li>
                <li>Processed snacks</li>
                <li>Sugary drinks</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>The Thermic Effect of Food</h3>
            <p>
              Foods that require more effort to chew and digest actually burn
              more calories during the digestion process. This includes:
            </p>
            <ul>
              <li>Fruits and vegetables</li>
              <li>Lean meats</li>
              <li>Whole grains</li>
              <li>Foods high in protein</li>
            </ul>
            <p>
              Additionally, certain foods like coffee, tea, chilies, cinnamon,
              and ginger have been found to increase the rate of calories
              burned.
            </p>
          </div>
        </section>
      </main>
    </div>
    <script>
      // Tab switching
      var tabs = document.querySelectorAll(".tab-button");
      tabs.forEach(function (tab) {
        tab.addEventListener("click", function () {
          tabs.forEach(function (t) {
            t.classList.remove("active");
          });
          this.classList.add("active");
        });
      });

      // Calculate button
      document
        .querySelector(".calculate-button")
        .addEventListener("click", function () {
          var age = parseInt(document.getElementById("age").value);
          var gender = document.querySelector(
            'input[name="gender"]:checked'
          ).value;
          var height = parseFloat(document.getElementById("height").value);
          var weight = parseFloat(document.getElementById("weight").value);
          var activity = document.getElementById("activity").value;

          var activityFactors = {
            sedentary: 1.2,
            light: 1.375,
            moderate: 1.55,
            very: 1.725,
            super: 1.9,
          };

          var bmr;
          if (gender === "male") {
            bmr = 10 * weight + 6.25 * height - 5 * age + 5;
          } else {
            bmr = 10 * weight + 6.25 * height - 5 * age - 161;
          }
          var calories = bmr * activityFactors[activity];
          document.getElementById("result").innerHTML =
            "Estimated daily calories: " + Math.round(calories);
        });

      // Clear button
      document
        .querySelector(".clear-button")
        .addEventListener("click", function () {
          document.getElementById("calorie-form").reset();
          document.getElementById("result").innerHTML = "";
        });

      // Settings link (placeholder)
      document
        .querySelector(".settings-link a")
        .addEventListener("click", function (e) {
          e.preventDefault();
          alert("Settings not implemented");
        });

      // Food Energy Converter
      function convertEnergy() {
        var value =
          parseFloat(document.getElementById("converter-value").value) || 0;
        var fromUnit = document.getElementById("from-unit").value;
        var toUnit = document.getElementById("to-unit").value;

        // Conversion factors to kilojoules
        var toKJ = {
          kcal: 4.1868,
          cal: 0.0041868,
          kj: 1,
          j: 0.001,
        };

        // Convert to kilojoules first, then to target unit
        var kj = value * toKJ[fromUnit];
        var result = kj / toKJ[toUnit];

        document.getElementById("converted-value").textContent =
          result.toFixed(4);
      }

      // Add event listeners for converter
      document
        .getElementById("converter-value")
        .addEventListener("input", convertEnergy);
      document
        .getElementById("from-unit")
        .addEventListener("change", convertEnergy);
      document
        .getElementById("to-unit")
        .addEventListener("change", convertEnergy);

      // Initialize converter
      convertEnergy();
    </script>
  </body>
</html>
